#!/usr/bin/env python3
"""
E3 Device Designation Automation Script

This script automatically updates device designations in E3.series projects based on the 
sheet and grid position of the topmost leftmost pin of the first symbol of each device.

The designation format is: {device letter code}{sheet}{grid}

For devices with conflicting designations, letter suffixes (A, B, C, etc.) are appended
similar to the wire numbering logic.

Author: Assistant
Date: 2025-01-03
"""

import logging
import sys
import os
import re
from typing import Dict, List, Tuple, Optional
import win32com.client

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('device_designation.log'),
        logging.StreamHandler(sys.stdout)
    ]
)


class DeviceDesignationManager:
    """Manages device designation automation for E3.series projects"""
    
    def __init__(self):
        self.app = None
        self.job = None
        self.device = None
        self.symbol = None
        self.sheet = None
        self.pin = None
        
    def connect_to_e3(self):
        """Connect to E3 application"""
        try:
            self.app = win32com.client.GetActiveObject("CT.Application")
            self.job = self.app.CreateJobObject()
            self.device = self.job.CreateDeviceObject()
            self.symbol = self.job.CreateSymbolObject()
            self.sheet = self.job.CreateSheetObject()
            self.pin = self.job.CreatePinObject()
            
            logging.info("Successfully connected to E3 application")
            return True
            
        except Exception as e:
            logging.error(f"Failed to connect to E3 application: {e}")
            return False
    
    def extract_grid_position(self, grid_desc: str) -> str:
        """
        Extract grid position from grid description string.
        
        Args:
            grid_desc: Grid description in format "/sheet.grid" or similar
            
        Returns:
            Grid position string (e.g., "A1", "B2")
        """
        if not grid_desc:
            return ""
            
        # Extract grid part after the last dot
        parts = grid_desc.split('.')
        if len(parts) >= 2:
            return parts[-1]
        
        return grid_desc
    
    def get_device_letter_code(self, device_id: int) -> str:
        """
        Get the device letter code from device attributes or name.
        
        Args:
            device_id: Device ID
            
        Returns:
            Device letter code (e.g., "M", "K", "T")
        """
        try:
            self.device.SetId(device_id)
            
            letercode = self.device.GetComponentAttributeValue("DeviceLetterCode")
            return letercode
            
            
        except Exception as e:
            logging.error(f"Error getting device letter code for device {device_id}: {e}")
            return "X"
    
    def get_topmost_leftmost_symbol_position(self, device_id: int) -> Tuple[Optional[str], Optional[str]]:
        """
        Get the sheet and grid position of the topmost leftmost pin of the first symbol.

        Args:
            device_id: Device ID

        Returns:
            Tuple of (sheet_assignment, grid_position) or (None, None) if not found
        """
        try:
            self.device.SetId(device_id)

            # Get all symbols for this device - try both with and without parameter
            symbol_ids_result = self.device.GetSymbolIds()  # Try without parameter first
            logging.info(f"Device {device_id} GetSymbolIds() result: {symbol_ids_result} (type: {type(symbol_ids_result)})")

            # If that doesn't work, try with parameter
            if not symbol_ids_result or symbol_ids_result == 0:
                symbol_ids_result = self.device.GetSymbolIds(1)  # 1 = placed symbols only
                logging.info(f"Device {device_id} GetSymbolIds(1) result: {symbol_ids_result} (type: {type(symbol_ids_result)})")

            if not symbol_ids_result or symbol_ids_result == 0:
                logging.info(f"No placed symbols found for device {device_id}")
                return None, None

            # Handle the result format - E3 returns (count, (None, symbol_id1, symbol_id2, ...))
            symbol_ids = []
            if isinstance(symbol_ids_result, tuple) and len(symbol_ids_result) >= 2:
                count = symbol_ids_result[0]
                symbol_tuple = symbol_ids_result[1]

                if isinstance(symbol_tuple, tuple) and len(symbol_tuple) > 1:
                    # Skip the first None element and get the actual symbol IDs
                    for sid in symbol_tuple[1:]:  # Skip the first None element
                        if sid is not None and isinstance(sid, int) and sid > 0:
                            symbol_ids.append(sid)
                        elif sid is not None:
                            logging.debug(f"Skipping invalid symbol ID {sid} (type: {type(sid)}) for device {device_id}")

                logging.debug(f"Device {device_id}: Found {len(symbol_ids)} valid symbol IDs from {count} total")
            else:
                logging.debug(f"Unexpected symbol IDs format for device {device_id}: {symbol_ids_result}")
                return None, None

            if not symbol_ids:
                logging.info(f"No valid symbol IDs found for device {device_id}")
                return None, None
            
            best_position = None
            best_sheet = None
            best_x = float('inf')
            best_y = float('inf')
            
            # Check each symbol to find the topmost leftmost position
            for symbol_id in symbol_ids:
                try:
                    # Validate symbol_id before using it
                    if not isinstance(symbol_id, int) or symbol_id <= 0:
                        logging.debug(f"Skipping invalid symbol ID {symbol_id} for device {device_id}")
                        continue

                    self.symbol.SetId(symbol_id)

                    # Get symbol schema location
                    result = self.symbol.GetSchemaLocation()
                    logging.info(f"Symbol {symbol_id} GetSchemaLocation result: {result} (type: {type(result)})")

                    if not result or result == 0:
                        logging.info(f"No schema location for symbol {symbol_id} of device {device_id}")
                        continue

                    # GetSchemaLocation returns (sheet_id, x, y, grid_desc, column, row)
                    # But the actual return format might vary
                    if isinstance(result, tuple) and len(result) >= 4:
                        sheet_id, x_pos, y_pos, grid_desc = result[:4]

                        # Validate the returned values
                        if not isinstance(sheet_id, int) or sheet_id <= 0:
                            logging.debug(f"Invalid sheet_id {sheet_id} for symbol {symbol_id} of device {device_id}")
                            continue
                        if not isinstance(x_pos, (int, float)) or not isinstance(y_pos, (int, float)):
                            logging.debug(f"Invalid position ({x_pos}, {y_pos}) for symbol {symbol_id} of device {device_id}")
                            continue
                    else:
                        # If it returns just sheet_id, we need to get other values separately
                        logging.debug(f"Unexpected schema location format for symbol {symbol_id} of device {device_id}: {result}")
                        continue

                    # Get sheet assignment (page number)
                    if sheet_id and sheet_id > 0:
                        self.sheet.SetId(sheet_id)
                        sheet_assignment = self.sheet.GetAssignment()

                        # Try alternative methods if GetAssignment() returns empty
                        if not sheet_assignment:
                            try:
                                sheet_assignment = self.sheet.GetName()
                                logging.debug(f"Using sheet name: {sheet_assignment}")
                            except:
                                pass

                        if not sheet_assignment:
                            try:
                                sheet_assignment = str(sheet_id)
                                logging.debug(f"Using sheet ID as assignment: {sheet_assignment}")
                            except:
                                pass

                        grid_position = self.extract_grid_position(grid_desc)

                        logging.info(f"Symbol {symbol_id}: sheet_id={sheet_id}, sheet_assignment='{sheet_assignment}', grid_desc='{grid_desc}', grid_position='{grid_position}', pos=({x_pos}, {y_pos})")

                        # Check if this position is more topmost/leftmost
                        if (y_pos < best_y) or (y_pos == best_y and x_pos < best_x):
                            best_x = x_pos
                            best_y = y_pos
                            best_sheet = sheet_assignment
                            best_position = grid_position

                except Exception as e:
                    logging.debug(f"Error processing symbol {symbol_id} for device {device_id}: {e}")
                    continue
            
            if best_sheet and best_position:
                return best_sheet, best_position
            else:
                logging.debug(f"Could not determine position for device {device_id}")
                return None, None

        except Exception as e:
            logging.error(f"Error getting symbol position for device {device_id}: {e}")
            return None, None

    def generate_device_designation(self, letter_code: str, sheet: str, grid: str) -> str:
        """
        Generate device designation from components.

        Args:
            letter_code: Device letter code (e.g., "M", "K")
            sheet: Sheet assignment/page number
            grid: Grid position (e.g., "A1", "B2")

        Returns:
            Device designation string
        """
        return f"{letter_code}{sheet}{grid}"

    def assign_suffix_for_conflicts(self, designations: Dict[str, List[int]]) -> Dict[int, str]:
        """
        Assign letter suffixes for conflicting device designations.

        Args:
            designations: Dictionary mapping base designation to list of device IDs

        Returns:
            Dictionary mapping device ID to final designation with suffix
        """
        final_designations = {}

        for base_designation, device_ids in designations.items():
            if len(device_ids) == 1:
                # No conflict, use base designation
                final_designations[device_ids[0]] = base_designation
            else:
                # Multiple devices with same base designation, sort by position and add suffixes
                logging.info(f"Found {len(device_ids)} devices with designation '{base_designation}', adding suffixes")

                # Sort devices by their leftmost position for consistent ordering
                device_positions = []
                for device_id in device_ids:
                    try:
                        self.device.SetId(device_id)
                        symbol_ids_result = self.device.GetSymbolIds()
                        if symbol_ids_result[0] > 0:
                            symbol_ids = [sid for sid in symbol_ids_result[1] if sid is not None]
                            if symbol_ids:
                                self.symbol.SetId(symbol_ids[0])
                                location = self.symbol.GetSchemaLocation()
                                if len(location) >= 3:
                                    x_pos = location[1]
                                    device_positions.append((x_pos, device_id))
                    except Exception as e:
                        logging.debug(f"Could not get position for device {device_id}: {e}")
                        # If we can't get position, add with default position
                        device_positions.append((0.0, device_id))

                # Sort by x position (left to right)
                device_positions.sort(key=lambda x: x[0])

                # Assign suffixes - first device keeps original designation, others get suffixes
                for i, (_, device_id) in enumerate(device_positions):
                    if i == 0:
                        # First device keeps the original designation (no suffix)
                        final_designations[device_id] = base_designation
                        logging.info(f"Device {device_id} assigned designation: {base_designation} (first device, no suffix)")
                    else:
                        # Subsequent devices get suffixes starting with A
                        suffix = chr(ord('A') + i - 1)  # i-1 so second device gets 'A', third gets 'B', etc.
                        final_designation = f"{base_designation}.{suffix}"
                        final_designations[device_id] = final_designation
                        logging.info(f"Device {device_id} assigned designation: {final_designation}")

        return final_designations

    def update_device_designation(self, device_id: int, designation: str) -> bool:
        """
        Update the device designation attribute.

        Args:
            device_id: Device ID
            designation: New designation

        Returns:
            True if successful, False otherwise
        """
        try:
            self.device.SetId(device_id)

            # Use SetName() to set the device designation
            result = self.device.SetName(designation)
            if result > 0:  # Success
                logging.info(f"Updated device {device_id} designation to '{designation}' using SetName()")
                return True
            else:
                logging.warning(f"SetName() failed for device {device_id}, result: {result}")
                return False

        except Exception as e:
            logging.error(f"Error updating designation for device {device_id}: {e}")
            return False

    def process_devices(self):
        """Process all devices in the project"""
        try:
            # Get all device IDs
            device_ids_result = self.job.GetAllDeviceIds()
            if not device_ids_result:
                logging.warning("No devices found in project")
                return

            # Handle the result format similar to wire numbering script
            actual_devices = []
            if isinstance(device_ids_result, tuple) and len(device_ids_result) >= 2:
                count = device_ids_result[0]
                device_ids = device_ids_result[1]

                logging.info(f"E3 reports {count} devices")

                if isinstance(device_ids, tuple):
                    # Filter out None values
                    actual_devices = [did for did in device_ids if did is not None]
                else:
                    actual_devices = [device_ids] if device_ids is not None else []
            else:
                logging.warning(f"Unexpected device IDs format: {type(device_ids_result)}")
                return

            if not actual_devices:
                logging.warning("No valid device IDs found")
                return

            logging.info(f"Processing {len(actual_devices)} devices")

            # Collect device data
            device_data = {}
            designations = {}  # base_designation -> [device_ids]
            devices_with_symbols = 0
            devices_without_symbols = 0

            for device_id in actual_devices:
                try:
                    # Get device letter code
                    letter_code = self.get_device_letter_code(device_id)

                    # Get position of topmost leftmost symbol
                    sheet, grid = self.get_topmost_leftmost_symbol_position(device_id)

                    if sheet and grid:
                        # Generate base designation
                        base_designation = self.generate_device_designation(letter_code, sheet, grid)

                        # Store device data
                        device_data[device_id] = {
                            'letter_code': letter_code,
                            'sheet': sheet,
                            'grid': grid,
                            'base_designation': base_designation
                        }

                        # Track designations for conflict resolution
                        if base_designation not in designations:
                            designations[base_designation] = []
                        designations[base_designation].append(device_id)

                        logging.info(f"Device {device_id}: {letter_code} at sheet {sheet}, grid {grid} -> {base_designation}")
                        devices_with_symbols += 1
                    else:
                        logging.debug(f"Could not determine position for device {device_id}, skipping")
                        devices_without_symbols += 1

                except Exception as e:
                    logging.error(f"Error processing device {device_id}: {e}")
                    devices_without_symbols += 1
                    continue

            logging.info(f"Found {devices_with_symbols} devices with placed symbols, {devices_without_symbols} devices without placed symbols")

            # Resolve conflicts and assign final designations
            final_designations = self.assign_suffix_for_conflicts(designations)

            # Update device designations
            success_count = 0
            for device_id, final_designation in final_designations.items():
                if self.update_device_designation(device_id, final_designation):
                    success_count += 1

            logging.info(f"Successfully updated {success_count} out of {len(final_designations)} device designations")

        except Exception as e:
            logging.error(f"Error in process_devices: {e}")

    def run(self):
        """Main execution method"""
        try:
            logging.info("Starting E3 Device Designation Automation")

            if not self.connect_to_e3():
                return False

            self.process_devices()

            logging.info("Device designation automation completed")
            return True

        except Exception as e:
            logging.error(f"Error in main execution: {e}")
            return False
        finally:
            # Clean up COM objects
            self.app = None
            self.job = None
            self.device = None
            self.symbol = None
            self.sheet = None
            self.pin = None


def main():
    """Main entry point"""
    try:
        manager = DeviceDesignationManager()
        success = manager.run()

        if success:
            print("Device designation automation completed successfully!")
            return 0
        else:
            print("Device designation automation failed!")
            return 1

    except Exception as e:
        logging.error(f"Unexpected error in main: {e}")
        print(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
